﻿using Klee.Domain.Messages.Queries.OperatorManagement.Operators.Data;
using Klee.Web.App.Pages.OperatorManagement.Operators.OperatorCreate;
using Klee.Web.App.Pages.OperatorManagement.Operators.OperatorDetails;
using Klee.Web.App.Pages.OperatorManagement.Operators.OperatorList.Data;
using Renoir.Srp.Portal.Web.Pages.Common.Views.ListViews;
using Renoir.Web.Razor.Components.Dialogs.Data;
using Renoir.Web.Razor.Components.DxGrids.Events;
using Renoir.Web.Razor.Pages.Dialogs.Events;
using Renoir.Web.Razor.Services.UserNotifications.Helpers;

namespace Klee.Web.App.Pages.OperatorManagement.Operators.OperatorList;

public partial class OperatorListViewBase
    : LayoutBodyListDxGridViewBase<OperatorListViewModel, OperatorListItem, UserSessionData>
{
    
    #region PROPERTIES
    // Dialog Views
    protected OperatorCreateDialogView OperatorCreateDialogView { get; set; }
    #endregion

    #region CONSTRUCTORS
    protected OperatorListViewBase() { }
    #endregion


    #region METHODS - OVERRIDE
    protected override async Task OnInitializedAsync()
    {
        // Init
        ViewModel = new OperatorListViewModel(this);

        //
        await base.OnInitializedAsync();
    }

    protected override async Task OnParametersSetAsync()
    {
        try
        {
            // Load Operators
            if (await ViewModel.LoadOperatorsAsync())
            {
                //...
            }

            //
            await base.OnParametersSetAsync();
        }
        catch (Exception exception)
        {
            #region Logging
            Logger.LogError(exception, "Exception when setting parameters.");
            #endregion

            if (!IsDisposed)
            {
                // Notify
                await UserNotificationService.ShowErrorAsync(exception.Message);
            }

            //throw;
        }
    }

    #endregion


    #region EVENT HANDLERS - DX DATA GRID
    protected async Task OnDxGrid_ClickViewDetails(RDxGridRowViewDetailsMouseEventArgs e)
    {
        // Init
        OperatorListItem operatorListItem = e.GetDataItem() as OperatorListItem ?? new OperatorListItem();
        string operatorId = operatorListItem.OperatorId ?? "";

        try
        {
            // View Operator
            await this.NavigateToAsync(OperatorDetailsView.GetUri(operatorId), e.MustOpenInBlankBrowser);
        }
        catch (Exception exception)
        {
            #region Logging
            Logger.LogError(exception, "Exception when going to view details of operator '{operatorId}'", operatorId);
            #endregion

            // Notify
            await UserNotificationService.ShowErrorAsync(exception.Message);

            //throw;
        }
    }

    protected async Task OnDxGrid_ClickDelete(RDxGridRowDeleteMouseEventArgs e)
    {
        // Init
        OperatorListItem operatorListItem = e.GetDataItem() as OperatorListItem ?? new OperatorListItem();
        string operatorId = operatorListItem.OperatorId ?? "";

        try
        {
            if (await DialogBox.AskConfirmationForWarningAsync("Delete operator?",
                                                                    $"Delete operator with id '{operatorId}'?") == RDialogResult.Ok)
            {
                // Delete Operator
                await ViewModel.DeleteOperatorAsync(operatorId);

                // Notify
                await UserNotificationService.ShowSuccessAsync("Deleted", $"Operator with id '{operatorId}' is deleted.");
            }
        }
        catch (Exception exception)
        {
            #region Logging
            Logger.LogError(exception, "Exception when deleting operator with id '{operatorId}'", operatorId);
            #endregion

            // Notify
            await UserNotificationService.ShowErrorAsync(exception.Message);

            //throw;
        }
    }
    #endregion

    #region EVENT HANDLERS
    protected async Task OnClickNew()
    {
        try {
            await this.OperatorCreateDialogView.ViewModel.LoadOrganizationNamesAsync();
            await OperatorCreateDialogView.OpenDialogViewAsync();
        }
        catch (Exception exception)
        {
            #region Logging
            Logger.LogError(exception, "Exception when opening dialog '{dialogViewName}'", nameof(OperatorCreateDialogView));
            #endregion

            // Notify
            await UserNotificationService.ShowErrorAsync(exception.Message);

            //throw;
        }
    }
    #endregion

    #region EVENT HANDLERS - Operator CREATE DIALOG VIEW
    protected void OnOperatorCreateDialogView_Opened()
    {
    }

    protected async Task OnOperatorCreateDialogView_Closed(RDialogViewClosedEventArgs e)
    {
        switch (e.DialogResult)
        {
            case RDialogResult.Ok:
                // Refresh the page to show the newly created Operator
                //this.NavigationManager.NavigateTo(this.NavigationManager.Uri, forceLoad: true);
                await ViewModel.LoadOperatorsAsync(true);
                break;

            default:
                break;
        }
    }
    #endregion

    #region EVENT HANDLERS
    //protected async Task OnClick_MainFilterNavItemAllOperators(MouseEventArgs e)
    //{
    //    // Set
    //    this.ViewModel.OperatorsMainFilterTypeId = OperatorsMainFilterTypeIds.All;

    //    // Load
    //    // Todo: Could load be based on property change of OperatorsMainFilterType?
    //    await this.ViewModel.LoadOperatorsAsync();
    //}

    public async void OnValueChanged_OperatorsMainFilterTypeId(OperatorsMainFilterTypeIds operatorsMainFilterTypeId)
    {
        try
        {
            // Set
            ViewModel.OperatorsMainFilterTypeId = operatorsMainFilterTypeId;

            // Load
            // Todo: Could load be based on property change of OperatorsMainFilterType?
            await ViewModel.LoadOperatorsAsync();
        }
        catch (Exception exception)
        {
            #region Logging
            Logger.LogError(exception, "Exception when selecting filter type in '{viewName}'", nameof(OperatorListView));
            #endregion

            // Notify
            await UserNotificationService.ShowErrorAsync(exception.Message);

            //throw;
        }
    }
    #endregion
}