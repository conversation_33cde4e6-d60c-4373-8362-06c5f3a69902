using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Klee.Domain.Entities.VehicleManagement.Vehicles.Data;
using Klee.Domain.Entities.VoyageManagement.Voyages.Data;
using Klee.Domain.Messages.Queries.VoyageManagement.Voyages;
using Klee.Domain.Messages.Queries.VoyageManagement.Voyages.Data;
using Klee.Domain.Services.Repositories.VoyageManagement;
using Klee.Domain.Services.UserContextService;
using Microsoft.EntityFrameworkCore;
using Paramore.Darker;

namespace Klee.Domain.Services.QueryHandlers.VoyageManagement.Voyages;

public sealed class GetVoyageManagementListQueryHandler
    : QueryHandlerAsync<GetVoyageManagementListQuery, IReadOnlyList<VoyageManagementListItem>>
{
    #region PROPERTIES
    private IVoyageSrpRepository VoyageSrpRepository { get; }
    private IUserContextHelperService UserContextHelperService { get; }
    #endregion

    #region CONSTRUCTORS
    public GetVoyageManagementListQueryHandler(IVoyageSrpRepository voyageSrpRepository, IUserContextHelperService userContextHelperService)
    {
        this.VoyageSrpRepository = voyageSrpRepository;
        this.UserContextHelperService = userContextHelperService;
    }
    #endregion

    #region METHODS
    public override async Task<IReadOnlyList<VoyageManagementListItem>> ExecuteAsync(
        GetVoyageManagementListQuery query,
        CancellationToken cancellationToken = new CancellationToken())
    {
        // Get Organization ID from logged in user
        string organizationId = await UserContextHelperService.GetUserOrganizationIdByClaimsAsync(query.Context.User);

        // Get voyages with all related data
        List<VoyageManagementListItem> voyageListItems = await VoyageSrpRepository.Entities(query)
            .Include(v => v.Vehicle)
            .Include(v => v.Operator)
                .ThenInclude(o => o.Organization)
            .Include(v => v.BookingOrganization)
            .Where(v => 
                // Outgoing voyages: booked by current organization
                v.BookingOrganizationId == organizationId ||
                // Incoming voyages: operator belongs to current organization
                (v.Operator != null && v.Operator.OrganizationId == organizationId))
            .OrderByDescending(v => v.StartDateTime)
            .Select(v => new VoyageManagementListItem
            {
                VoyageId = v.VoyageId,
                StartDateTime = v.StartDateTime,
                EndDateTime = v.EndDateTime,
                Description = v.Description,
                RequiredQualifications = v.RequiredQualifications,
                Status = v.Status,

                // Determine voyage type
                VoyageType = v.BookingOrganizationId == organizationId
                    ? VoyageTypeIds.Outgoing
                    : VoyageTypeIds.Incoming,
                
                // Booking organization
                BookingOrganizationId = v.BookingOrganizationId,
                BookingOrganizationName = v.BookingOrganization.Name,
                
                // Vehicle information
                VehicleId = v.VehicleId,
                VehicleName = v.Vehicle != null ? v.Vehicle.VehicleName : "",
                VehicleENI = v.Vehicle != null ? v.Vehicle.ENI : "",
                VehicleType = v.Vehicle != null ? v.Vehicle.VehicleTypeId : VehicleTypeIds.None,
                VehicleTypeDisplayName = v.Vehicle != null ? v.Vehicle.VehicleTypeId.ToString() : "",
                VehicleLength = v.Vehicle != null ? v.Vehicle.Length : 0,
                VehicleBeam = v.Vehicle != null ? v.Vehicle.Beam : 0,
                VehicleHourlyRateInEuros = v.Vehicle != null ? v.Vehicle.HourlyRateInEuros : 0,
                VehicleOrganizationId = v.Vehicle != null ? v.Vehicle.OrganizationId : "",
                VehicleOrganizationName = v.Vehicle != null ? v.Vehicle.Organization.Name : "",
                
                // Operator information (if assigned)
                OperatorId = v.OperatorId ?? "",
                OperatorFirstName = v.Operator != null ? v.Operator.FirstName : "",
                OperatorLastName = v.Operator != null ? v.Operator.LastName : "",
                OperatorEmail = v.Operator != null ? v.Operator.OperatorEmail : "",
                OperatorBiography = v.Operator != null ? v.Operator.Biography : "",
                OperatorYearsOfExperience = v.Operator != null ? v.Operator.YearsOfExperience : 0,
                OperatorHourlyRateInEuros = v.Operator != null ? v.Operator.HourlyRateInEuros : 0.0,
                OperatorOrganizationId = v.Operator != null ? v.Operator.OrganizationId : "",
                OperatorOrganizationName = v.Operator != null ? v.Operator.Organization.Name : ""
            })
            .ToListAsync(cancellationToken);

        // Filter by IsActive if specified
        if (query.IsActive != null)
        {
            // Note: IsActive filtering would need to be implemented based on business logic
            // For now, we'll return all voyages as the Voyage entity has IsActive property
        }

        foreach (VoyageManagementListItem voyageManagementListItem in voyageListItems) {
            voyageManagementListItem.StartDateTime = voyageManagementListItem.StartDateTime.ToLocalTime();
            voyageManagementListItem.EndDateTime = voyageManagementListItem.EndDateTime.ToLocalTime();
        }

        return voyageListItems;
    }
    #endregion
}
