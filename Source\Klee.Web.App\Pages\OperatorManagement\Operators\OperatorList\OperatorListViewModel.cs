﻿using EnumsNET;
using Klee.Domain.Messages.Commands.OperatorManagement.Operators;
using Renoir.Srp.Portal.Web.Pages.Common;
using Klee.Domain.Messages.Queries.OperatorManagement.Operators.Data;
using Klee.Domain.Messages.Queries.OperatorManagement.Operators;
using ReactiveUI;
using Renoir.Web.Razor.Pages.Common;
using Klee.Web.App.Pages.OperatorManagement.Operators.OperatorList.Data;

namespace Klee.Web.App.Pages.OperatorManagement.Operators.OperatorList;

public class OperatorListViewModel
    : ViewModelBase<OperatorListViewModel>, IRViewModelWithUserSessionData<UserSessionData>
{
    #region PROPERTIES - STATIC
    // Selectable
    public static IList<OperatorsMainFilterTypeIds> SelectableOperatorsMainFilterTypeIds { get; }
        = Enums.GetMembers<OperatorsMainFilterTypeIds>()
               .Where(_ => _.Value != OperatorsMainFilterTypeIds.None)
               .Select(_ => _.Value).ToList();
    #endregion

    #region FIELDS
    private OperatorsMainFilterTypeIds _OperatorsMainFilterTypeId = OperatorsMainFilterTypeIds.None;
    #endregion

    #region PROPERTIES
    public OperatorsMainFilterTypeIds OperatorsMainFilterTypeId
    {
        get => _OperatorsMainFilterTypeId;
        set => this.RaiseAndSetIfChanged(ref _OperatorsMainFilterTypeId, value);
    }

    // Actual Loaded
    public OperatorsMainFilterTypeIds LoadedOperatorsMainFilterTypeId { get; private set; }

    //
    public IReadOnlyList<OperatorListItem> Operators { get; private set; } = new List<OperatorListItem>();
    #endregion

    #region CONSTRUCTORS
    public OperatorListViewModel(IViewModelHost viewModelHost)
        : base(viewModelHost)
    {
    }
    #endregion

    #region METHODS - OVERRIDES
    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();
    }
    #endregion

    #region METHODS - USER SESSION DATA
    public void SetUserSessionData(UserSessionData userSessionData)
    {
        OperatorsMainFilterTypeId = userSessionData.OperatorsMainFilterTypeId;
    }

    public UserSessionData GetUserSessionData()
    {
        return new UserSessionData()
        {
            OperatorsMainFilterTypeId = LoadedOperatorsMainFilterTypeId
        };
    }
    #endregion

    #region METHODS
    public async Task<bool> LoadOperatorsAsync(bool forceLoad = false)
    {
        // Adjust Properties
        OperatorsMainFilterTypeId = OperatorsMainFilterTypeId != OperatorsMainFilterTypeIds.None ? OperatorsMainFilterTypeId : OperatorsMainFilterTypeIds.All;

        // Load Operators
        if (LoadedOperatorsMainFilterTypeId != OperatorsMainFilterTypeId ||
            forceLoad)
        {
            switch (OperatorsMainFilterTypeId)
            {
                case OperatorsMainFilterTypeIds.Active:
                    Operators = await SrpQueryProcessor.ExecuteAsync(new GetOperatorListQuery(SrpQueryContext)
                    {
                        IsActive = true
                    });
                    break;
                case OperatorsMainFilterTypeIds.All:
                default:
                    Operators = await SrpQueryProcessor.ExecuteAsync(new GetOperatorListQuery(SrpQueryContext));
                    break;
            }

            // Set
            LoadedOperatorsMainFilterTypeId = OperatorsMainFilterTypeId;

            // Notify
            await InvokeStateHasChangedOnHostAsync();

            return true;
        }
        else
        {
            return false;
        }
    }

    public async Task DeleteOperatorAsync(string operatorId)
    {
        try
        {
            // Delete Operator
            await SrpCommandProcessor.SendAsync(new DeleteOperatorCommand(operatorId,
                                                                               SrpCommandContext));

            // Load Operators
            await LoadOperatorsAsync(forceLoad: true);
        }
        catch
        {
            throw;
        }
    }
    #endregion
}