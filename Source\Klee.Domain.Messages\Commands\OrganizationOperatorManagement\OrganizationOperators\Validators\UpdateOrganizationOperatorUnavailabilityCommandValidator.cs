using FluentValidation;

namespace Klee.Domain.Messages.Commands.OrganizationOperatorManagement.OrganizationOperators.Validators;

public class UpdateOrganizationOperatorUnavailabilityCommandValidator : AbstractValidator<UpdateOrganizationOperatorUnavailabilityCommand>
{
    public UpdateOrganizationOperatorUnavailabilityCommandValidator()
    {
        this.RuleFor(_ => _.OperatorId).NotNull();
        this.RuleFor(_ => _.OperatorId).NotEmpty();
        this.RuleFor(_ => _.UnavailabilityPeriods).NotNull();
        
        this.RuleForEach(_ => _.UnavailabilityPeriods)
            .Must(period => period.StartDate <= period.EndDate)
            .WithMessage("Start date must be before or equal to end date");
    }
}
