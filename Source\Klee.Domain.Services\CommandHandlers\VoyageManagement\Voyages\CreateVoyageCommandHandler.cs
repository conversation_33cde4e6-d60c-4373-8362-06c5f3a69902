using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Klee.Domain.Entities.Common.Exceptions.VoyageManagement;
using Klee.Domain.Entities.OrganizationManagement.Organizations;
using Klee.Domain.Entities.VehicleManagement.Vehicles;
using Klee.Domain.Entities.VoyageManagement.Voyages;
using Klee.Domain.Entities.VoyageManagement.Voyages.Data;
using Klee.Domain.Messages.Commands.VoyageManagement.Voyages;
using Klee.Domain.Services.MessagingService;
using Klee.Domain.Services.Repositories.OrganizationManagement;
using Klee.Domain.Services.Repositories.VehicleManagement;
using Klee.Domain.Services.Repositories.VoyageManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.Extensions.Logging;
using Paramore.Brighter;
using Renoir.Application.EF.Data.Domains.Srp;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestContexts;
using Renoir.Application.Messages.Commands.Common.Helpers;

namespace Klee.Domain.Services.CommandHandlers.VoyageManagement.Voyages
{
    public sealed class CreateVoyageCommandHandler
        : RequestHandlerAsync<CreateVoyageCommand>
    {
        #region PROPERTIES
        private IVoyageSrpRepository VoyageSrpRepository { get; }
        private IAppSrpDbContext DbContext { get; }
        private IVoyageNotificationService VoyageNotificationService { get; }
        private IVehicleSrpRepository VehicleSrpRepository { get; }
        private IOrganizationSrpRepository OrganizationSrpRepository { get; }
        private ILogger<CreateVoyageCommandHandler> Logger { get; }
        #endregion

        #region CONSTRUCTORS
        public CreateVoyageCommandHandler(IVoyageSrpRepository voyageSrpRepository,
                                          IAppSrpDbContext dbContext,
                                          IVoyageNotificationService voyageNotificationService,
                                          IVehicleSrpRepository vehicleSrpRepository,
                                          IOrganizationSrpRepository organizationSrpRepository,
                                          ILogger<CreateVoyageCommandHandler> logger)
        {
            VoyageSrpRepository = voyageSrpRepository;
            DbContext = dbContext;
            VoyageNotificationService = voyageNotificationService;
            VehicleSrpRepository = vehicleSrpRepository;
            OrganizationSrpRepository = organizationSrpRepository;
            Logger = logger;
        }
        #endregion

        #region METHODS
        [RequestContextCommandHandler(step: 1, HandlerTiming.Before)]
        public override async Task<CreateVoyageCommand> HandleAsync(CreateVoyageCommand command,
            CancellationToken cancellationToken = new ())
        {
            // Use database transaction to prevent race conditions (e.g., multiple bookings at the same time)
            await using IDbContextTransaction transaction = await DbContext.Database.BeginTransactionAsync(cancellationToken);

            try
            {
                // Final availability check within transaction - this prevents race conditions (e.g., multiple bookings at the same time)
                if (!string.IsNullOrEmpty(command.OperatorId))
                {
                    // Check for conflicting voyages with 15-minute buffer
                    TimeSpan buffer = TimeSpan.FromMinutes(15);
                    DateTime queryStartWithBuffer = command.StartDateTime.ToUniversalTime().Subtract(buffer);
                    DateTime queryEndWithBuffer = command.EndDateTime.ToUniversalTime().Add(buffer);

                    bool hasConflictingVoyages = await DbContext.Set<Voyage>()
                        .AnyAsync(v =>
                                v.IsActive == true &&
                                v.OperatorId == command.OperatorId &&
                                v.EndDateTime > queryStartWithBuffer &&
                                v.StartDateTime < queryEndWithBuffer,
                            cancellationToken);

                    if (hasConflictingVoyages)
                    {
                        throw new VoyageConcurrencyException(
                            command.OperatorId,
                            command.StartDateTime,
                            command.EndDateTime);
                    }
                }

                // Create Voyage with appropriate initial status
                Voyage voyage = new Voyage()
                {
                    BookingOrganizationId = command.BookingOrganizationId,
                    StartDateTime = command.StartDateTime.ToUniversalTime(),
                    EndDateTime = command.EndDateTime.ToUniversalTime(),
                    Description = command.Description,
                    RequiredQualifications = command.RequiredQualifications,
                    VehicleId = command.VehicleId,
                    OperatorId = command.OperatorId,
                    Status = string.IsNullOrEmpty(command.OperatorId) ? VoyageStatus.Open : VoyageStatus.Booked
                };

                // Save within transaction
                await VoyageSrpRepository.AddAsync(voyage, command);

                // Commit transaction
                await transaction.CommitAsync(cancellationToken);

                // Set Result
                command.Result.VoyageId = voyage.VoyageId;

                // Send voyage opportunity notifications if no operator assigned (fire and forget)
                if (string.IsNullOrEmpty(voyage.OperatorId))
                {
                    try {
                        // Get required data for notifications
                        Vehicle vehicle = await VehicleSrpRepository.FindAsync(
                            v => v.VehicleId == voyage.VehicleId && v.EntityPartitionKey == voyage.VehicleId, command);
                        Organization bookingOrganization = await OrganizationSrpRepository.FindAsync(
                            o => o.OrganizationId == voyage.BookingOrganizationId &&
                                 o.EntityPartitionKey == voyage.BookingOrganizationId, command);
                        List<Organization> targetOrganizations = await OrganizationSrpRepository.Entities(command)
                            .Where(o => o.OrganizationId != voyage.BookingOrganizationId && o.IsActive == true)
                            .ToListAsync(cancellationToken: cancellationToken);

                        if (vehicle != null && bookingOrganization != null) {

                            _ = Task.Run(async () => {
                                await VoyageNotificationService.SendVoyageOpportunityNotificationsAsync(voyage, vehicle,
                                    bookingOrganization, targetOrganizations);
                            }, cancellationToken);
                        }

                        Logger.LogInformation("Initiated voyage opportunity notifications for voyage {VoyageId}", voyage.VoyageId);
                    }
                    catch (Exception ex)
                    {
                        Logger.LogError(ex, "Failed to initiate voyage opportunity notifications for voyage {VoyageId}", voyage.VoyageId);
                        // Don't rethrow - we don't want notification failures to affect voyage creation
                    }
                }
            }
            catch (VoyageConcurrencyException)
            {
                // Re-throw
                await transaction.RollbackAsync(cancellationToken);
                throw;
            }
            catch (DbUpdateException ex)
            {
                // Handle database constraint violations
                await transaction.RollbackAsync(cancellationToken);
                throw new VoyageConcurrencyException(
                    command.OperatorId,
                    command.StartDateTime,
                    command.EndDateTime,
                    "The captain is no longer available for the requested time slot. Another booking may have been made.",
                    ex);
            }
            catch (Exception ex)
            {
                // Handle any other unexpected errors
                await transaction.RollbackAsync(cancellationToken);
                throw new System.InvalidOperationException("An error occurred while creating the voyage.", ex);
            }

            return await base.HandleAsync(command, cancellationToken).ConfigureAwait(ContinueOnCapturedContext);
        }
        #endregion
    }
}
