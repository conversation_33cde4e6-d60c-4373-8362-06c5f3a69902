﻿using Klee.Domain.Entities.OperatorManagement.Operators;
using Klee.Domain.Entities.OperatorManagement.Operators.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Klee.Infrastructure.Data.EntityTypeConfigurations.OperatorManagement;

public class OperatorEntityTypeConfiguration : IEntityTypeConfiguration<Operator>
{
    public void Configure(EntityTypeBuilder<Operator> builder)
    {
        //builder.AddCosmosDbProperties();
        builder.HasIndex(_ => _.OperatorId)
            .IsUnique();
        builder.Property(_ => _.OperatorId)
            .IsRequired();
        builder.Property(_ => _.OrganizationId)
            .IsRequired();
        builder.HasQueryFilter(_ => _.EntityIsDeleted == false);

        //Set up the relationship with org
        builder
            .HasOne(s => s.Organization)
            .WithMany(o => o.Operators)
            .HasForeignKey(s => s.OrganizationId)
            .HasPrincipalKey(r => r.OrganizationId);

        // Configure UnavailabilityPeriods as owned entities
        builder.OwnsMany(o => o.UnavailabilityPeriods, up =>
        {
            up.Property(p => p.StartDate).IsRequired();
            up.Property(p => p.EndDate).IsRequired();
            up.Property(p => p.Reason).IsRequired();
        });
    }
}