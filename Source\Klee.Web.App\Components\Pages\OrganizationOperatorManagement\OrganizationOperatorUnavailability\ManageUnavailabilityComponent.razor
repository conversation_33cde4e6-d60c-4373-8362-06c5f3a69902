@using Klee.Domain.Entities.OperatorManagement.Operators.Data
@using Monet.Helpers

@inherits ManageUnavailabilityViewBase

@if (IsLoading)
{
    <div class="flex justify-center items-center py-8">
        <Spin Size="@SpinSize.Large" />
    </div>
}
else
{
    <div class="space-y-6">
        <!-- Add New Unavailable Date -->
        <div class="bg-gray-50 p-4 rounded-lg">
            <h3 class="text-lg font-medium text-teal-700 mb-4">Add unavailability period</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-teal-700 mb-2">Date</label>
                    <RangePicker 
                            TValue="DateTime?[]"
                                 OnChange="OnDateRangeChanged"
                                 Class="@TailwindStyleStrings.Form.Input"/>
                </div>

                <div>
                    <label class="block text-sm font-medium text-teal-700 mb-2">Reason</label>
                    <EnumSelect TEnum="UnavailabilityReasonIds"
                                @bind-Value="@SelectedReason"
                                Placeholder="Select reaason for unavailability"
                                Class="@TailwindStyleStrings.Form.Select"
                                AllowClear="false"
                                Id="unavailabilityType">
                    </EnumSelect>
                </div>
            </div>

            <Button Type="@ButtonType.Primary"
                    Class="@TailwindStyleStrings.Button.Primary"
                    OnClick="OnClick_AddDate">
                <i class="fas fa-plus h-4 w-4 mr-2"></i>
                Add Date
            </Button>
        </div>

        <!-- Calendar View -->
        <div class="bg-white p-4 rounded-lg border border-gray-200">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-teal-700">Unavailability Calendar</h3>
                <span class="text-sm text-gray-600">Click on unavailable dates to remove them</span>
            </div>
            <div class="calendar-container" style="max-height: 400px; overflow-y: auto;">
                <Calendar DateCellRender="CalenderDateCellRender" />
            </div>
        </div>

    </div>
}

@code {

        private RenderFragment CalenderDateCellRender(DateTime value) {
            value = DateTime.SpecifyKind(value, DateTimeKind.Local);
            List<OperatorUnavailabilityData> unavailabilityData = GetUnavailabilityData(value);
            string tooltipContent = GetTooltipContent(value, unavailabilityData);
            bool hasUnavailability = unavailabilityData.Any();

            return @<Template>
                <Tooltip Title="@tooltipContent">
                    <div class="@(hasUnavailability ? "cursor-pointer hover:bg-gray-100 rounded p-1" : "")"
                         @onclick="@(() => HandleCalendarDateClick(value))"
                         @onclick:stopPropagation="true">
                        <ul class="unavailability-events">
                            @foreach (OperatorUnavailabilityData data in unavailabilityData)
                            {
                                <li key="@data.Content">
                                    <Badge Status="@data.Status" Text="@data.Content" />
                                </li>
                            }
                        </ul>
                    </div>
                </Tooltip>
            </Template>;
        }

}



