using System;
using System.Threading;
using System.Threading.Tasks;
using Klee.Domain.Entities.VoyageManagement.Voyages;
using Klee.Domain.Entities.VoyageManagement.Voyages.Data;
using Klee.Domain.Messages.Commands.VoyageManagement.Voyages;
using Klee.Domain.Services.Repositories.VoyageManagement;
using Microsoft.Extensions.Logging;
using Paramore.Brighter;
using Renoir.Application.EF.Data.Domains.Srp;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestContexts;

namespace Klee.Domain.Services.CommandHandlers.VoyageManagement.Voyages
{
    public sealed class StartVoyageCommandHandler
        : RequestHandlerAsync<StartVoyageCommand>
    {
        #region PROPERTIES
        private IVoyageSrpRepository VoyageSrpRepository { get; }
        private IAppSrpDbContext DbContext { get; }
        private ILogger<StartVoyageCommandHandler> Logger { get; }
        #endregion

        #region CONSTRUCTORS
        public StartVoyageCommandHandler(IVoyageSrpRepository voyageSrpRepository,
                                        IAppSrpDbContext dbContext,
                                        ILogger<StartVoyageCommandHandler> logger)
        {
            VoyageSrpRepository = voyageSrpRepository;
            DbContext = dbContext;
            Logger = logger;
        }
        #endregion

        #region METHODS
        [RequestContextCommandHandler(step: 1, HandlerTiming.Before)]
        public override async Task<StartVoyageCommand> HandleAsync(StartVoyageCommand command,
            CancellationToken cancellationToken = new())
        {
            try
            {
                // Find the voyage
                Voyage voyage = await VoyageSrpRepository.FindAsync(v => v.VoyageId == command.VoyageId &&
                                                                         v.EntityPartitionKey == command.VoyageId.ToString(), command);

                if (voyage == null)
                {
                    throw new InvalidOperationException($"Voyage with ID {command.VoyageId} not found.");
                }

                // Verify voyage is in Booked status
                if (voyage.Status != VoyageStatus.Booked)
                {
                    throw new InvalidOperationException($"Voyage must be in 'Booked' status to start. Current status: {voyage.Status}");
                }

                // Update voyage status to Executing
                voyage.Status = VoyageStatus.Executing;

                // Update voyage
                await VoyageSrpRepository.UpdateAsync(voyage, command);

                // Set result
                command.Result.VoyageId = voyage.VoyageId;
                command.Result.Success = true;

                Logger.LogInformation($"Voyage {command.VoyageId} started successfully");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"Error starting voyage {command.VoyageId}");
                throw;
            }

            return await base.HandleAsync(command, cancellationToken).ConfigureAwait(ContinueOnCapturedContext);
        }
        #endregion
    }
}
