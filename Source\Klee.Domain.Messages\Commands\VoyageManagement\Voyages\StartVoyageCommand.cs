using System;
using Renoir.Application.Messages.Commands.Common;

namespace Klee.Domain.Messages.Commands.VoyageManagement.Voyages;

public class StartVoyageCommand : CommandBase
{
    #region RESULT CLASS
    public class CommandResult
    {
        public Guid VoyageId { get; set; }
        public bool Success { get; set; }
    }
    #endregion

    #region PROPERTIES
    public Guid VoyageId { get; }
    
    // Result
    public CommandResult Result { get; } = new CommandResult();
    #endregion

    #region CONSTRUCTORS
    public StartVoyageCommand(Guid voyageId, ICommandContext commandContext)
        : base(commandContext)
    {
        this.VoyageId = voyageId;
    }
    #endregion
}
