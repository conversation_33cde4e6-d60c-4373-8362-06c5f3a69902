using System;
using System.Threading;
using System.Threading.Tasks;
using Klee.Domain.Entities.Common.Exceptions;
using Klee.Domain.Entities.OperatorManagement.Operators;
using Klee.Domain.Entities.OperatorManagement.Operators.Data;
using Klee.Domain.Messages.Commands.OrganizationOperatorManagement.OrganizationOperators;
using Klee.Domain.Messages.Commands.OrganizationOperatorManagement.OrganizationOperators.Validators;
using Klee.Domain.Services.Repositories.OperatorManagement;
using Paramore.Brighter;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestContexts;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestValidation;

namespace Klee.Domain.Services.CommandHandlers.OrganizationOperatorManagement.OrganizationOperators;

public sealed class UpdateOrganizationOperatorUnavailabilityCommandHandler
    : RequestHandlerAsync<UpdateOrganizationOperatorUnavailabilityCommand>
{
    #region PROPERTIES
    private IOperatorSrpRepository OperatorSrpRepository { get; }
    #endregion

    #region CONSTRUCTORS
    public UpdateOrganizationOperatorUnavailabilityCommandHandler(IOperatorSrpRepository operatorSrpRepository)
    {
        this.OperatorSrpRepository = operatorSrpRepository;
    }
    #endregion

    #region METHODS
    [RequestContextCommandHandler(step: 1, HandlerTiming.Before)]
    [RequestValidationCommandHandler(step: 2, HandlerTiming.Before, typeof(UpdateOrganizationOperatorUnavailabilityCommandValidator))]
    public override async Task<UpdateOrganizationOperatorUnavailabilityCommand> HandleAsync(UpdateOrganizationOperatorUnavailabilityCommand command,
        CancellationToken cancellationToken = new CancellationToken())
    {
        try {


            // Get Operator (if it exists) 
            if (await OperatorSrpRepository.ExistsAsync(_ => _.OperatorId == command.OperatorId &&
                                                             _.EntityPartitionKey == command.OperatorId,
                    command)) {
                Operator operatorObj = await OperatorSrpRepository.FindAsync(_ => _.OperatorId == command.OperatorId &&
                        _.EntityPartitionKey == command.OperatorId,
                    command);

                // Update unavailability periods with UTC values
                foreach (OperatorUnavailabilityPeriod operatorUnavailabilityPeriod in command.UnavailabilityPeriods) {
                    operatorUnavailabilityPeriod.StartDate = operatorUnavailabilityPeriod.StartDate.ToUniversalTime();
                    operatorUnavailabilityPeriod.EndDate = operatorUnavailabilityPeriod.EndDate.ToUniversalTime();
                }

                operatorObj.UnavailabilityPeriods = command.UnavailabilityPeriods;

                // Update

                await OperatorSrpRepository.UpdateAsync(operatorObj, command);

                // Set Result
                command.Result.EntityId = operatorObj.EntityId;
            }
            else {
                throw new EntityNotFoundException(
                    $"Operator with operator id '{command.OperatorId}' not found.");
            }

            return await base.HandleAsync(command, cancellationToken).ConfigureAwait(ContinueOnCapturedContext);
        }
        catch (Exception ex)
        {
            // Handle any exceptions that may occur
            throw;
        }
    }
    #endregion
}
