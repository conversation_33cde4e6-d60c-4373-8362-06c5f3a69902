using System;
using Klee.Domain.Messages.Commands.VoyageManagement.Voyages;
using Klee.Domain.Messages.Queries.VoyageManagement.Voyages;
using Klee.Domain.Messages.Queries.VoyageManagement.Voyages.Data;
using Klee.Domain.Services;
using Renoir.Application.Messages.Queries.Common;

namespace Klee.Web.App.Components.Pages.VoyageManagement.VoyageList;

public class VoyagesViewModel
{
    #region FIELDS
    private ISrpProcessors _srpProcessors;
    #endregion

    #region PROPERTIES
    public List<VoyageManagementListItem> Voyages { get; private set; } = new();
    #endregion

    #region CONSTRUCTORS
    public VoyagesViewModel(ISrpProcessors srpProcessors)
    {
        this._srpProcessors = srpProcessors;
    }
    #endregion

    #region METHODS
    public async Task<bool> LoadVoyagesAsync(bool forceLoad = false)
    {
        // Load Voyages
        if (forceLoad)
        {
            // Load Voyages
            IQueryContext queryContext = await _srpProcessors.GetQueryContextAsync();
            this.Voyages = (await this._srpProcessors.QueryProcessor.ExecuteAsync(new GetVoyageManagementListQuery(queryContext))).ToList();
            return true;
        }
        else
        {
            return false;
        }
    }

    public bool CanStartVoyage(Guid voyageId, out string errorMessage)
    {
        errorMessage = "";

        var voyage = Voyages.FirstOrDefault(v => v.VoyageId == voyageId);
        if (voyage == null)
        {
            errorMessage = "Voyage not found.";
            return false;
        }

        var currentTime = DateTime.Now;
        var voyageStartTime = voyage.StartDateTime.ToLocalTime();
        var timeDifference = voyageStartTime - currentTime;

        // Allow starting 15 minutes before scheduled time
        if (timeDifference.TotalMinutes > 15)
        {
            var canStartAt = voyageStartTime.AddMinutes(-15);
            errorMessage = $"Voyage can only be started within 15 minutes of the scheduled start time. You can start this voyage at {canStartAt:MMM dd, yyyy HH:mm}.";
            return false;
        }

        return true;
    }

    public async Task<bool> StartVoyageAsync(Guid voyageId)
    {
        try
        {
            var command = new StartVoyageCommand(voyageId, await _srpProcessors.GetCommandContextAsync());
            await _srpProcessors.CommandProcessor.SendAsync(command);
            return command.Result.Success;
        }
        catch
        {
            return false;
        }
    }

    public async Task<bool> StopVoyageAsync(Guid voyageId)
    {
        try
        {
            var command = new StopVoyageCommand(voyageId, await _srpProcessors.GetCommandContextAsync());
            await _srpProcessors.CommandProcessor.SendAsync(command);
            return command.Result.Success;
        }
        catch
        {
            return false;
        }
    }
    #endregion
}
