using System;
using System.Threading.Tasks;
using Klee.Domain.Services;
using Klee.Web.App.Services.Authentication;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using AntDesign;

namespace Klee.Web.App.Components.Pages.VoyageManagement.VoyageList;

public class VoyagesViewBase : ComponentBase
{
    #region DI
    [Inject]
    protected NavigationManager NavigationManager { get; set; }

    [Inject]
    private INotificationService NotificationService { get; set; }

    [Inject]
    private ILogger<VoyagesViewBase> Logger { get; set; }

    [Inject]
    private ISrpProcessors SrpProcessors { get; set; }

    [Inject]
    private IUserAuthenticationService UserAuthenticationService { get; set; }
    #endregion

    #region PROPERTIES
    protected VoyagesViewModel ViewModel { get; set; }

    //User authorization
    public bool IsUserOrganizationAdmin { get; set; } = false;

    // Modal state for voyage start confirmation
    protected bool ShowStartVoyageModal { get; set; } = false;
    protected Guid SelectedVoyageId { get; set; }
    protected string SelectedVoyageDescription { get; set; } = "";

    // Modal state for voyage stop confirmation
    protected bool ShowStopVoyageModal { get; set; } = false;
    protected Guid SelectedStopVoyageId { get; set; }
    protected string SelectedStopVoyageDescription { get; set; } = "";
    #endregion

    #region METHODS - OVERRIDE
    protected override async Task OnInitializedAsync()
    {
        // Init
        this.ViewModel = new VoyagesViewModel(this.SrpProcessors);
        await base.OnInitializedAsync();
    }

    protected override async Task OnParametersSetAsync()
    {
        try
        {
            // Check user authorization using the user authentication service
            this.IsUserOrganizationAdmin = await this.UserAuthenticationService.IsCurrentUserOrganizationAdminAsync();

            // Load Voyages
            await this.ViewModel.LoadVoyagesAsync(forceLoad: true);

            //
            await base.OnParametersSetAsync();
        }
        catch (Exception exception)
        {
            #region Logging
            this.Logger.LogError(exception, "Exception when setting parameters.");
            #endregion
        }
    }
    #endregion

    #region METHODS
    protected async Task OnClickRefresh()
    {
        try
        {
            await this.ViewModel.LoadVoyagesAsync(forceLoad: true);
            StateHasChanged();
        }
        catch (Exception exception)
        {
            this.Logger.LogError(exception, "Exception when refreshing voyage data.");

        }
    }

    protected void OnStartVoyage(Guid voyageId)
    {
        // Validate if voyage can be started
        if (!this.ViewModel.CanStartVoyage(voyageId, out string errorMessage))
        {
            NotificationService.Error(new NotificationConfig()
            {
                Message = "Cannot Start Voyage",
                Description = errorMessage,
                Duration = 6.0
            });
            return;
        }

        // Show confirmation modal
        var voyage = this.ViewModel.Voyages.FirstOrDefault(v => v.VoyageId == voyageId);
        if (voyage != null)
        {
            SelectedVoyageId = voyageId;
            SelectedVoyageDescription = $"Start voyage for {voyage.VesselDisplayName} scheduled at {voyage.StartDateTime.ToLocalTime():MMM dd, yyyy HH:mm}?";
            ShowStartVoyageModal = true;
            StateHasChanged();
        }
    }

    protected async Task ConfirmStartVoyage()
    {
        try
        {
            ShowStartVoyageModal = false;
            bool success = await this.ViewModel.StartVoyageAsync(SelectedVoyageId);
            if (success)
            {
                NotificationService.Success(new NotificationConfig()
                {
                    Message = "Voyage Started",
                    Description = "The voyage has been started successfully!",
                    Duration = 4.5
                });
                await this.ViewModel.LoadVoyagesAsync(forceLoad: true);
                StateHasChanged();
            }
            else
            {
                NotificationService.Error(new NotificationConfig()
                {
                    Message = "Error",
                    Description = "Failed to start the voyage. Please try again.",
                    Duration = 4.5
                });
            }
        }
        catch (Exception exception)
        {
            this.Logger.LogError(exception, "Exception when starting voyage.");
            NotificationService.Error(new NotificationConfig()
            {
                Message = "Error",
                Description = "An error occurred while starting the voyage.",
                Duration = 4.5
            });
        }
    }

    protected void CancelStartVoyage()
    {
        ShowStartVoyageModal = false;
        StateHasChanged();
    }

    protected void OnStopVoyage(Guid voyageId)
    {
        // Show confirmation modal
        var voyage = this.ViewModel.Voyages.FirstOrDefault(v => v.VoyageId == voyageId);
        if (voyage != null)
        {
            SelectedStopVoyageId = voyageId;
            SelectedStopVoyageDescription = $"Stop voyage for {voyage.VesselDisplayName} scheduled at {voyage.StartDateTime.ToLocalTime():MMM dd, yyyy HH:mm}?";
            ShowStopVoyageModal = true;
            StateHasChanged();
        }
    }

    protected async Task ConfirmStopVoyage()
    {
        try
        {
            ShowStopVoyageModal = false;
            bool success = await this.ViewModel.StopVoyageAsync(SelectedStopVoyageId);
            if (success)
            {
                NotificationService.Success(new NotificationConfig()
                {
                    Message = "Voyage Completed",
                    Description = "The voyage has been completed successfully and an invoice has been created!",
                    Duration = 4.5
                });
                await this.ViewModel.LoadVoyagesAsync(forceLoad: true);
                StateHasChanged();
            }
            else
            {
                NotificationService.Error(new NotificationConfig()
                {
                    Message = "Error",
                    Description = "Failed to complete the voyage. Please try again.",
                    Duration = 4.5
                });
            }
        }
        catch (Exception exception)
        {
            this.Logger.LogError(exception, "Exception when stopping voyage.");
            NotificationService.Error(new NotificationConfig()
            {
                Message = "Error",
                Description = "An error occurred while completing the voyage.",
                Duration = 4.5
            });
        }
    }

    protected void CancelStopVoyage()
    {
        ShowStopVoyageModal = false;
        StateHasChanged();
    }
    #endregion
}
