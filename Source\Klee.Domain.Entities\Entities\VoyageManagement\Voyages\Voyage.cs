﻿using Klee.Domain.Entities.OperatorManagement.Operators;
using Klee.Domain.Entities.OrganizationManagement.Organizations;
using Klee.Domain.Entities.QualificationManagement.Qualifications.Data;
using Klee.Domain.Entities.VehicleManagement.Vehicles;
using Klee.Domain.Entities.VoyageManagement.Voyages.Data;
using Renoir.Application.Domain;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System;
using Monet.Helpers;

namespace Klee.Domain.Entities.VoyageManagement.Voyages;

public class Voyage: DomainEntityAggregateRootBase<Guid> {

    #region PROPERTIES - IDENTIFICATION

    /// <summary>
    /// The Seafar internal id of the Voyage
    /// </summary>
    [Required]
    public Guid VoyageId { get; internal set; } = Guid.NewGuid();

    /// <summary>
    /// Organization that books the Voyage
    /// </summary>
    [Required]
    public string BookingOrganizationId { get; internal set; } = "";
    public Organization BookingOrganization { get; internal set; }

    /// <summary>
    /// Start date and time of the Voyage
    /// </summary>
    [Required] 
    public DateTime StartDateTime { get; internal set; } = DateTime.UtcNow;

    /// <summary>
    /// End date and time of the Voyage
    /// </summary>

    [Required]
    public DateTime EndDateTime { get; internal set; } = DateTime.UtcNow;

    /// <summary>
    /// Small description of the Voyage for adding extra information
    /// </summary>
    public string Description { get; internal set; } = "";
    /// <summary>
    /// Required qualifications for the Voyage
    /// </summary>
    public List<QualificationTypeIds> RequiredQualifications { get; internal set; } = new();

    #endregion

    #region PROPERTIES - CORE  COMPONENTS

    [Required]
    public string VehicleId { get; internal set; } = "";
    public Vehicle? Vehicle { get; internal set; }

    public string? OperatorId { get; internal set; } = "";
    public Operator? Operator { get; internal set; }

    #endregion

    #region PROPERTIES - STATUS
    /// <summary>
    /// Current status of the voyage (Open, Booked, Executing, Completed)
    /// </summary>
    [Required]
    public VoyageStatus Status { get; internal set; } = VoyageStatus.Open;

    #endregion

    #region PROPERTIES - MANAGEMENT
    public bool NeedsOperator => OperatorId.IsNullOrEmpty();

    #endregion


    #region PROPERTIES - SYSTEM
    /// <summary>
    /// Is active when the Voyage is still in active use
    /// </summary>
    public bool? IsActive { get; internal set; } = true;

    #endregion

    #region METHODS - ENTITY
    public override string CreateEntityPartitionKey()
    {
        return this.VoyageId.ToString();
    }

    public override string GetEntityId2()
    {
        return this.VoyageId.ToString();
    }

    public override string GetEntityTypeName()
    {
        return "Voyage";
    }
    #endregion

}