using System;
using System.Collections.Generic;
using Klee.Domain.Entities.QualificationManagement.Qualifications.Data;
using Klee.Domain.Entities.VehicleManagement.Vehicles.Data;
using Klee.Domain.Entities.VoyageManagement.Voyages.Data;

namespace Klee.Domain.Messages.Queries.VoyageManagement.Voyages.Data;

public class VoyageManagementListItem
{
    #region PROPERTIES - CORE
    public Guid VoyageId { get; set; }
    public DateTime StartDateTime { get; set; }
    public DateTime EndDateTime { get; set; }
    public string Description { get; set; } = "";
    public List<QualificationTypeIds> RequiredQualifications { get; set; } = new();
    public VoyageTypeIds VoyageType { get; set; } = VoyageTypeIds.None;
    public VoyageStatus Status { get; set; } = VoyageStatus.Open;
    #endregion

    #region PROPERTIES - VESSEL INFORMATION
    public string VehicleId { get; set; } = "";
    public string VehicleName { get; set; } = "";
    public string VehicleENI { get; set; } = "";
    public VehicleTypeIds VehicleType { get; set; } = VehicleTypeIds.None;
    public string VehicleTypeDisplayName { get; set; } = "";
    public double VehicleLength { get; set; } = 0.0;
    public double VehicleBeam { get; set; } = 0.0;
    public double VehicleHourlyRateInEuros { get; set; } = 0.0;
    public string VehicleOrganizationId { get; set; } = "";
    public string VehicleOrganizationName { get; set; } = "";
    #endregion

    #region PROPERTIES - OPERATOR INFORMATION
    public string OperatorId { get; set; } = "";
    public string OperatorFirstName { get; set; } = "";
    public string OperatorLastName { get; set; } = "";
    public string OperatorEmail { get; set; } = "";
    public string OperatorBiography { get; set; } = "";
    public int OperatorYearsOfExperience { get; set; } = 0;
    public double OperatorHourlyRateInEuros { get; set; } = 0.0;
    public string OperatorOrganizationId { get; set; } = "";
    public string OperatorOrganizationName { get; set; } = "";
    #endregion

    #region PROPERTIES - BOOKING ORGANIZATION
    public string BookingOrganizationId { get; set; } = "";
    public string BookingOrganizationName { get; set; } = "";
    #endregion

    #region PROPERTIES - COMPUTED
    public string OperatorDisplayName => $"{OperatorFirstName} {OperatorLastName}".Trim();
    public string VesselDisplayName => $"{VehicleName} ({VehicleOrganizationName})";
    public string OperatorDisplayNameWithOrg => $"{OperatorDisplayName} ({OperatorOrganizationName})";
    public string VoyageTypeDisplayName => VoyageType.ToString();
    public string DateRangeDisplay => StartDateTime.Date == EndDateTime.Date 
        ? $"{StartDateTime:MMM dd, yyyy} ({StartDateTime:HH:mm} - {EndDateTime:HH:mm})"
        : $"{StartDateTime:MMM dd, yyyy HH:mm} - {EndDateTime:MMM dd, yyyy HH:mm}";
    public TimeSpan Duration => EndDateTime - StartDateTime;
    public string DurationDisplay => Duration.TotalDays >= 1 
        ? $"{Duration.Days}d {Duration.Hours}h {Duration.Minutes}m"
        : $"{Duration.Hours}h {Duration.Minutes}m";
    #endregion
}
