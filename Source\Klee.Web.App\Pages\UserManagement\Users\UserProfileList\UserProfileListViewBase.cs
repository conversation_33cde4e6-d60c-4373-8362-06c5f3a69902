﻿using Klee.Domain.Messages.Queries.UserManagement.Users.Data;
using Klee.Web.App.Pages.UserManagement.Users.UserProfileCreate;
using Klee.Web.App.Pages.UserManagement.Users.UserProfileDetails;
using Klee.Web.App.Pages.UserManagement.Users.UserProfileList.Data;
using Renoir.Srp.Portal.Web.Pages.Common.Views.ListViews;
using Renoir.Web.Razor.Components.Dialogs.Data;
using Renoir.Web.Razor.Components.DxGrids.Events;
using Renoir.Web.Razor.Pages.Dialogs.Events;
using Renoir.Web.Razor.Services.UserNotifications.Helpers;

namespace Klee.Web.App.Pages.UserManagement.Users.UserProfileList;

    public partial class UserProfileListViewBase 
        : LayoutBodyListDxGridViewBase<UserProfileListViewModel, UserProfileListItem, UserSessionData>
    {
        #region FIELDS
        #endregion

        #region PROPERTIES
        // Dialog Views
        protected UserProfileCreateDialogView UserProfileCreateDialogView { get; set; }
        #endregion

        #region CONSTRUCTORS
        protected UserProfileListViewBase()
        {
        }
        #endregion

        #region IDISPOSABLE
        protected override void Dispose(bool disposing)
        {
            //
            base.Dispose(disposing);
        }
        #endregion

        #region METHODS - OVERRIDE
        protected override async Task OnInitializedAsync()
        {
            // Init
            this.ViewModel = new UserProfileListViewModel(this);

            //
            await base.OnInitializedAsync();
        }

        protected override async Task OnParametersSetAsync()
        {
            try
            {
                // Load UserProfiles
                if (await this.ViewModel.LoadUserProfilesAsync())
                {
                    //...
                }

                //
                await base.OnParametersSetAsync();
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when setting parameters.");
                #endregion

                if (!this.IsDisposed)
                {
                    // Notify
                    await this.UserNotificationService.ShowErrorAsync(exception.Message);
                }

                //throw;
            }
        }

        #endregion

        #region METHODS
        #endregion

        #region EVENT HANDLERS - DX DATA GRID
        protected async Task OnDxGrid_ClickViewDetails(RDxGridRowViewDetailsMouseEventArgs e)
        {
            // Init
            UserProfileListItem userProfileListItem = e.GetDataItem() as UserProfileListItem ?? new UserProfileListItem();
            string userProfileId = userProfileListItem.UserId ?? "";

            try
            {
                // View UserProfile
                await this.NavigateToAsync(UserProfileDetailsView.GetUri(userProfileId), e.MustOpenInBlankBrowser);
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when going to view details of UserProfile '{UserProfileId}'", userProfileId);
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }

        protected async Task OnDxGrid_ClickDelete(RDxGridRowDeleteMouseEventArgs e)
        {
            // Init
            UserProfileListItem userProfileListItem = e.GetDataItem() as UserProfileListItem ?? new UserProfileListItem();
            string userProfileId = userProfileListItem.UserId ?? "";

            try
            {
                if (await this.DialogBox.AskConfirmationForWarningAsync("Delete UserProfile?",
                                                                        $"Delete UserProfile with id '{userProfileId}'?") == RDialogResult.Ok)
                {
                    // Delete UserProfile
                    await this.ViewModel.DeleteUserProfileAsync(userProfileId);

                    // Notify
                    await this.UserNotificationService.ShowSuccessAsync("Deleted", $"UserProfile with id '{userProfileId}' is deleted.");
                }
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when deleting UserProfile with id '{UserProfileId}'", userProfileId);
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }
        #endregion

        #region EVENT HANDLERS
        protected async Task OnClickNew()
        {
            try {
                await this.UserProfileCreateDialogView.ViewModel.LoadOrganizationNamesAsync();
                await this.UserProfileCreateDialogView.OpenDialogViewAsync();
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when opening dialog '{dialogViewName}'", nameof(this.UserProfileCreateDialogView));
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }
        #endregion

        #region EVENT HANDLERS - UserProfile CREATE DIALOG VIEW
        protected void OnUserProfileCreateDialogView_Opened()
        {
        }

        protected async Task OnUserProfileCreateDialogView_Closed(RDialogViewClosedEventArgs e)
        {
            switch (e.DialogResult)
            {
                case RDialogResult.Ok:
                    // Refresh the page to show the newly created UserProfile
                    this.NavigationManager.NavigateTo(this.NavigationManager.Uri, forceLoad: true);
                    break;

                default:
                    break;
            }
        }
        #endregion

        #region EVENT HANDLERS
        //protected async Task OnClick_MainFilterNavItemAllUserProfiles(MouseEventArgs e)
        //{
        //    // Set
        //    this.ViewModel.UserProfilesMainFilterTypeId = UserProfilesMainFilterTypeIds.All;

        //    // Load
        //    // Todo: Could load be based on property change of UserProfilesMainFilterType?
        //    await this.ViewModel.LoadUserProfilesAsync();
        //}

        public async void OnValueChanged_UserProfilesMainFilterTypeId(UserProfilesMainFilterTypeIds userProfilesMainFilterTypeId)
        {
            try
            {
                // Set
                this.ViewModel.UserProfilesMainFilterTypeId = userProfilesMainFilterTypeId;

                // Load
                // Todo: Could load be based on property change of UserProfilesMainFilterType?
                await this.ViewModel.LoadUserProfilesAsync();
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when selecting filter type in '{viewName}'", nameof(UserProfileListView));
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }
        #endregion
    }