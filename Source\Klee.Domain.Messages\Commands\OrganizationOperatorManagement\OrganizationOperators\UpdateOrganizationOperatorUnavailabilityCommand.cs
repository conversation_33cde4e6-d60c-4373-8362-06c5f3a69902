using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Klee.Domain.Entities.OperatorManagement.Operators.Data;
using Renoir.Application.Messages.Commands.Common;

namespace Klee.Domain.Messages.Commands.OrganizationOperatorManagement.OrganizationOperators;

public class UpdateOrganizationOperatorUnavailabilityCommand : CommandBase
{
    #region RESULT CLASS
    public class CommandResult
    {
        public long EntityId { get; set; }
    }
    #endregion

    #region PROPERTIES
    [Required]
    public string OperatorId { get; set; } = "";

    [Required]
    public List<OperatorUnavailabilityPeriod> UnavailabilityPeriods { get; set; } = new();

    // Result
    public CommandResult Result { get; } = new CommandResult();
    #endregion

    #region CONSTRUCTORS
    public UpdateOrganizationOperatorUnavailabilityCommand(ICommandContext commandContext)
        : base(commandContext)
    {
    }
    #endregion
}
