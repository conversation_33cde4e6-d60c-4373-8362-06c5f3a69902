using System;
using System.ComponentModel.DataAnnotations;

namespace Klee.Domain.Entities.OperatorManagement.Operators.Data;

public class OperatorUnavailabilityPeriod
{
    /// <summary>
    /// Start date of the unavailability period
    /// </summary>
    [Required]
    public DateTime StartDate { get; set; }
    
    /// <summary>
    /// End date of the unavailability period
    /// </summary>
    [Required]
    public DateTime EndDate { get; set; }
    
    /// <summary>
    /// Reason for unavailability
    /// </summary>
    [Required]
    public UnavailabilityReasonIds Reason { get; set; } = UnavailabilityReasonIds.None;
    
}
